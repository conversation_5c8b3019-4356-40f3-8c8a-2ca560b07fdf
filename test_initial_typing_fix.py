#!/usr/bin/env python3
"""
Test script to verify that the initial typing fix resolves the cross-capture duplicate detection issue.
This test specifically addresses the scenario where typing was blocked by cross-capture duplicate detection.
"""

import tempfile
import os
from unittest.mock import Mock

def test_initial_typing_fix():
    """Test that initial typing works even when cross-capture duplicates exist."""
    print("Testing Initial Typing Fix")
    print("="*50)
    
    # Import the main module
    from main import ScreenTextTyper
    
    # Create a test instance with minimal setup
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            # Initialize only the necessary attributes for testing
            self.delay = 0.1
            self.error_rate = 0.05
            self.variance = 0.1
            self.text_stability_threshold = 1.0
            
            # Enhanced state tracking for incremental updates
            self.current_text = ""
            self.previous_text = ""
            self.typed_text = ""
            self.cursor_position = 0
            from collections import deque
            self.text_history = deque(maxlen=10)
            self.typing_active = False
            self.capture_active = False
            
            # Trailing space feature state tracking
            self.last_typing_completion_time = 0
            self.text_stable_since = 0
            self.trailing_space_added = False
            self.last_stable_text = ""
            
            # Duplicate line detection state tracking
            self.duplicate_lines_skipped = 0
            self.total_lines_processed = 0
            self.last_duplicate_detection_time = 0
            
            # Cursor artifact removal state tracking
            self.cursor_artifacts_removed = 0
            self.cursor_removal_enabled = True
            self.last_cursor_removal_time = 0
            
            # Persistent text tracking state
            self.temp_file_path = None
            self.cross_capture_duplicates_skipped = 0
            self.last_cross_capture_detection_time = 0
            self.temp_file_created = False
            
            # Mock threading lock
            self.lock = Mock()
            
            # Track typing calls for testing
            self.typing_calls = []
            
        def type_text_with_effects(self, text):
            """Mock typing method that records calls instead of actually typing."""
            self.typing_calls.append(text)
            print(f"MOCK TYPING: '{text[:50]}...'")
            
        def position_cursor_at_end(self):
            """Mock cursor positioning."""
            print(f"MOCK CURSOR POSITIONING: Moving to position {len(self.typed_text)}")
            
        def position_cursor_at(self, position):
            """Mock cursor positioning."""
            print(f"MOCK CURSOR POSITIONING: Moving to position {position}")
    
    # Create test instance
    typer = TestTyper()
    
    # Test 1: Create a temporary file with previous capture content
    print("\n=== Test 1: Setup Previous Capture File ===")
    test_text = "Hello, this is a test message from OCR."
    
    # Create temporary file and save previous capture
    temp_fd, temp_path = tempfile.mkstemp(prefix='autotyper_test_', suffix='.txt')
    typer.temp_file_path = temp_path
    typer.temp_file_created = True
    
    try:
        with os.fdopen(temp_fd, 'w') as f:
            f.write(test_text)
        print(f"Created temp file with content: '{test_text}'")
        
        # Test 2: Simulate initial OCR capture of the same text
        print("\n=== Test 2: Initial OCR Capture (Same Text) ===")
        typer.current_text = test_text
        typer.previous_text = ""
        typer.typed_text = ""  # This is the key - no text typed yet
        
        print(f"Current text: '{typer.current_text}'")
        print(f"Typed text: '{typer.typed_text}'")
        
        # Test 3: Check the fresh start condition
        print("\n=== Test 3: Check Fresh Start Condition ===")
        fresh_start_condition = not typer.typed_text or not typer.current_text.startswith(typer.typed_text)
        print(f"Fresh start condition: {fresh_start_condition}")
        print(f"Should bypass cross-capture duplicate detection: {fresh_start_condition}")
        
        # Test 4: Test the calculate_smart_append_suffix_with_file method
        print("\n=== Test 4: Test Suffix Calculation ===")
        suffix, resume_pos, skipped = typer.calculate_smart_append_suffix_with_file(typer.current_text)
        print(f"Suffix to type: '{suffix}'")
        print(f"Resume position: {resume_pos}")
        print(f"Skipped content: '{skipped}'")
        
        # Test 5: Process text changes (this should trigger typing)
        print("\n=== Test 5: Process Text Changes ===")
        typer.process_text_changes()
        
        # Test 6: Verify results
        print("\n=== Test 6: Verify Results ===")
        print(f"Typing calls made: {len(typer.typing_calls)}")
        if typer.typing_calls:
            print(f"Text that was typed: '{typer.typing_calls[0]}'")
        
        print(f"Final typed text: '{typer.typed_text}'")
        print(f"Cross-capture duplicates skipped: {typer.cross_capture_duplicates_skipped}")
        
        # Determine success
        success = len(typer.typing_calls) > 0 and typer.typing_calls[0] == test_text
        
        print("\n=== Test Results ===")
        if success:
            print("✓ SUCCESS: Initial typing works despite cross-capture duplicates!")
            print("✓ The fix correctly bypasses cross-capture duplicate detection for initial typing")
            print("✓ Text was typed as expected")
        else:
            print("✗ FAILURE: Initial typing was still blocked")
            print("✗ The fix did not resolve the cross-capture duplicate detection issue")
        
        return success
        
    finally:
        # Clean up temporary file
        if os.path.exists(temp_path):
            os.remove(temp_path)
            print(f"Cleaned up temp file: {temp_path}")

def test_incremental_typing_still_works():
    """Test that incremental typing with cross-capture duplicate detection still works correctly."""
    print("\n" + "="*50)
    print("Testing Incremental Typing Still Works")
    print("="*50)
    
    # This test ensures our fix doesn't break the existing functionality
    from main import ScreenTextTyper
    
    # Create a test instance (simplified version of the above)
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            # Minimal initialization
            self.current_text = ""
            self.previous_text = ""
            self.typed_text = ""
            self.cross_capture_duplicates_skipped = 0
            self.temp_file_path = None
            self.temp_file_created = False
            
        def load_previous_capture(self):
            return "Hello, this is a test"
            
        def detect_cross_capture_duplicates(self, prev, curr):
            # Simulate finding duplicates
            if prev in curr:
                return 1, len(prev), prev
            return 0, 0, ""
    
    typer = TestTyper()
    
    # Test scenario: We have already typed some text, and new text contains duplicates
    typer.typed_text = "Hello, this is a test"  # Already typed
    typer.current_text = "Hello, this is a test\nNew additional content here"
    
    print(f"Typed text: '{typer.typed_text}'")
    print(f"Current text: '{typer.current_text}'")
    
    # This should use cross-capture duplicate detection because typed_text is not empty
    suffix, resume_pos, skipped = typer.calculate_smart_append_suffix_with_file(typer.current_text)
    
    print(f"Suffix: '{suffix}'")
    print(f"Resume position: {resume_pos}")
    print(f"Skipped content: '{skipped}'")
    
    expected_suffix = "\nNew additional content here"
    success = suffix == expected_suffix
    
    if success:
        print("✓ SUCCESS: Incremental typing with cross-capture duplicate detection works correctly")
    else:
        print("✗ FAILURE: Incremental typing was affected by the fix")
    
    return success

if __name__ == "__main__":
    print("Autotyper Initial Typing Fix Test")
    print("="*60)
    
    try:
        # Test the initial typing fix
        test1_success = test_initial_typing_fix()
        
        # Test that incremental typing still works
        test2_success = test_incremental_typing_still_works()
        
        print("\n" + "="*60)
        print("OVERALL TEST RESULTS")
        print("="*60)
        
        if test1_success and test2_success:
            print("✓ ALL TESTS PASSED!")
            print("✓ Initial typing fix works correctly")
            print("✓ Incremental typing functionality preserved")
            print("\nThe autotyper should now type correctly after OCR capture!")
        else:
            print("✗ SOME TESTS FAILED")
            if not test1_success:
                print("✗ Initial typing fix needs more work")
            if not test2_success:
                print("✗ Incremental typing functionality was broken")
                
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
