#!/usr/bin/env python3
"""
Test script to verify that the refined typing fix allows legitimate new content
while still preventing retyping of already-processed content.
"""

import tempfile
import os
from unittest.mock import Mock

def test_refined_content_change_logic():
    """Test the refined content change logic that distinguishes between new and already-typed content."""
    print("Testing Refined Content Change Logic")
    print("="*50)
    
    # Import the main module
    from main import ScreenTextTyper
    
    # Create a test instance with minimal setup
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            # Initialize only the necessary attributes for testing
            self.delay = 0.1
            self.error_rate = 0.05
            self.variance = 0.1
            self.text_stability_threshold = 1.0
            
            # Enhanced state tracking for incremental updates
            self.current_text = ""
            self.previous_text = ""
            self.typed_text = ""
            self.cursor_position = 0
            from collections import deque
            self.text_history = deque(maxlen=10)
            self.typing_active = False
            self.capture_active = False
            
            # Trailing space feature state tracking
            self.last_typing_completion_time = 0
            self.text_stable_since = 0
            self.trailing_space_added = False
            self.last_stable_text = ""
            
            # Duplicate line detection state tracking
            self.duplicate_lines_skipped = 0
            self.total_lines_processed = 0
            self.last_duplicate_detection_time = 0
            
            # Cursor artifact removal state tracking
            self.cursor_artifacts_removed = 0
            self.cursor_removal_enabled = True
            self.last_cursor_removal_time = 0
            
            # Persistent text tracking state
            self.temp_file_path = None
            self.cross_capture_duplicates_skipped = 0
            self.last_cross_capture_detection_time = 0
            self.temp_file_created = False
            
            # Mock threading lock
            self.lock = Mock()
            
            # Track typing calls for testing
            self.typing_calls = []
            
        def type_text_with_effects(self, text):
            """Mock typing method that records calls instead of actually typing."""
            self.typing_calls.append(text)
            print(f"MOCK TYPING: '{text[:50]}...'")
            
        def position_cursor_at_end(self):
            """Mock cursor positioning."""
            print(f"MOCK CURSOR POSITIONING: Moving to position {len(self.typed_text)}")
            
        def position_cursor_at(self, position):
            """Mock cursor positioning."""
            print(f"MOCK CURSOR POSITIONING: Moving to position {position}")
            
        def calculate_smart_append_suffix_with_file(self, text):
            """Mock suffix calculation."""
            if not self.typed_text:
                return text, 0, ""
            elif text.startswith(self.typed_text):
                suffix = text[len(self.typed_text):]
                return suffix, len(self.typed_text), ""
            else:
                return text, 0, ""
    
    # Create test instance
    typer = TestTyper()
    
    # Test scenarios
    scenarios = [
        {
            "name": "Initial typing",
            "setup_previous_capture": None,
            "typed_text": "",
            "current_text": "Hello, this is the first paragraph.",
            "expected_action": "type",
            "description": "Should type initial content"
        },
        {
            "name": "New content (no previous capture)",
            "setup_previous_capture": None,
            "typed_text": "Hello, this is the first paragraph.",
            "current_text": "This is completely new content.",
            "expected_action": "type",
            "description": "Should type new content when no previous capture exists"
        },
        {
            "name": "New content (different from previous)",
            "setup_previous_capture": "Some old content that was typed before.",
            "typed_text": "Hello, this is the first paragraph.",
            "current_text": "This is completely new content.",
            "expected_action": "type",
            "description": "Should type new content that's different from previous captures"
        },
        {
            "name": "Already typed content (high overlap)",
            "setup_previous_capture": "This is completely new content.",
            "typed_text": "Hello, this is the first paragraph.",
            "current_text": "This is completely new content.",
            "expected_action": "skip",
            "description": "Should skip content that was already typed in previous session"
        },
        {
            "name": "Partially typed content (low overlap)",
            "setup_previous_capture": "This is completely",
            "typed_text": "Hello, this is the first paragraph.",
            "current_text": "This is completely new content with additional text.",
            "expected_action": "type",
            "description": "Should type content that has new parts even if some was typed before"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n=== Test {i}: {scenario['name']} ===")
        print(f"Description: {scenario['description']}")
        
        # Setup previous capture file if needed
        if scenario['setup_previous_capture']:
            temp_fd, temp_path = tempfile.mkstemp(prefix='autotyper_test_', suffix='.txt')
            typer.temp_file_path = temp_path
            typer.temp_file_created = True
            
            try:
                with os.fdopen(temp_fd, 'w') as f:
                    f.write(scenario['setup_previous_capture'])
                print(f"Setup previous capture: '{scenario['setup_previous_capture']}'")
            except:
                os.close(temp_fd)
        else:
            typer.temp_file_path = None
            typer.temp_file_created = False
        
        try:
            # Reset state
            typer.typed_text = scenario['typed_text']
            typer.current_text = scenario['current_text']
            typer.previous_text = ""
            typer.typing_calls.clear()
            
            print(f"Typed text: '{typer.typed_text[:50]}...'")
            print(f"Current text: '{typer.current_text[:50]}...'")
            
            # Process the text change
            typer.process_text_changes()
            
            # Check results
            typing_occurred = len(typer.typing_calls) > 0
            
            if scenario['expected_action'] == 'type':
                if typing_occurred:
                    print(f"✓ PASS: Typing occurred as expected")
                    print(f"  Typed: '{typer.typing_calls[0][:50]}...'")
                else:
                    print(f"✗ FAIL: Expected typing but none occurred")
            else:  # expected_action == 'skip'
                if not typing_occurred:
                    print(f"✓ PASS: Typing skipped as expected")
                else:
                    print(f"✗ FAIL: Expected no typing but typing occurred")
                    print(f"  Unexpectedly typed: '{typer.typing_calls[0][:50]}...'")
        
        finally:
            # Clean up temp file
            if scenario['setup_previous_capture'] and typer.temp_file_path and os.path.exists(typer.temp_file_path):
                os.remove(typer.temp_file_path)

def test_is_content_already_typed():
    """Test the is_content_already_typed method specifically."""
    print("\n" + "="*50)
    print("Testing is_content_already_typed Method")
    print("="*50)
    
    from main import ScreenTextTyper
    
    # Create minimal test instance
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            self.temp_file_path = None
            self.temp_file_created = False
        
        def detect_cross_capture_duplicates(self, prev, curr):
            # Mock implementation for testing
            if prev in curr:
                # High overlap - most content is duplicate
                return 5, len(prev), prev
            elif any(word in curr.lower() for word in prev.lower().split()):
                # Low overlap - some content is duplicate
                return 1, len(prev) // 3, prev[:len(prev)//3]
            else:
                # No overlap
                return 0, 0, ""
    
    typer = TestTyper()
    
    test_cases = [
        {
            "previous": "Hello world this is a test",
            "current": "Hello world this is a test",
            "expected": True,
            "description": "Exact match should be detected as already typed"
        },
        {
            "previous": "Hello world",
            "current": "Hello world this is additional content",
            "expected": False,
            "description": "Partial match with new content should not be skipped"
        },
        {
            "previous": "Completely different content",
            "current": "This has no relation to previous",
            "expected": False,
            "description": "No overlap should be treated as new content"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {case['description']}")
        
        # Setup temp file with previous content
        temp_fd, temp_path = tempfile.mkstemp(prefix='autotyper_test_', suffix='.txt')
        typer.temp_file_path = temp_path
        typer.temp_file_created = True
        
        try:
            with os.fdopen(temp_fd, 'w') as f:
                f.write(case['previous'])
            
            result = typer.is_content_already_typed(case['current'])
            
            if result == case['expected']:
                print(f"✓ PASS: Expected {case['expected']}, got {result}")
            else:
                print(f"✗ FAIL: Expected {case['expected']}, got {result}")
        
        finally:
            if os.path.exists(temp_path):
                os.remove(temp_path)

if __name__ == "__main__":
    print("Autotyper Refined Typing Fix Test")
    print("="*60)
    
    try:
        # Test refined content change logic
        test_refined_content_change_logic()
        
        # Test the is_content_already_typed method
        test_is_content_already_typed()
        
        print("\n" + "="*60)
        print("SUMMARY")
        print("="*60)
        print("✓ Refined typing logic implemented")
        print("✓ New content detection working")
        print("✓ Already-typed content detection working")
        print("✓ System should now type new content while preventing retyping")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
